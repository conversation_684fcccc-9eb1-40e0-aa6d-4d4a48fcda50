#!/usr/bin/env python3
"""
🔧 ADMIN PANEL - Backend dla panelu administratora
Discord Bybit Signal Monitor - Professional Admin Interface

Zaawansowany panel administratora z pełną funkcjonalnością:
- Zarządzanie strategiami tradingowymi
- Zarządzanie kanałami Discord (jeden kanał = jedna strategia)
- Konfiguracja whitelisty botów per kanał
- Konfiguracja parametrów systemowych
- Logi administratora i audyt

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
Wersja: 1.0 Admin Panel Edition
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.exceptions import BadRequest
import json

# Import modeli
from models.database import DatabaseManager
from models.strategy import Strategy, StrategyManager
from models.channel import DiscordChannel, ChannelManager
from models.config import SystemConfig, ConfigManager

# Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('admin_panel.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Inicjalizacja Flask
app = Flask(__name__)
app.config.update({
    'SECRET_KEY': os.getenv('SECRET_KEY', 'admin-panel-secret-key-2025'),
    'JSON_SORT_KEYS': False
})

# Inicjalizacja managerów
DB_PATH = os.getenv('DB_PATH', 'signals.db')
db_manager = DatabaseManager(DB_PATH)
strategy_manager = StrategyManager(db_manager)
channel_manager = ChannelManager(db_manager)
config_manager = ConfigManager(db_manager)

logger.info("🚀 Admin Panel initialized successfully")


def get_client_ip() -> str:
    """Pobiera IP klienta dla logów."""
    return request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)


def handle_api_error(func):
    """Dekorator do obsługi błędów API."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"API validation error: {e}")
            return jsonify({'error': str(e)}), 400
        except Exception as e:
            logger.error(f"API error in {func.__name__}: {e}")
            return jsonify({'error': 'Internal server error'}), 500
    wrapper.__name__ = func.__name__
    return wrapper


# ===== MAIN ADMIN PANEL ROUTES =====

@app.route('/')
def admin_dashboard():
    """🏠 Główna strona panelu administratora."""
    logger.info("🏠 Admin dashboard requested")
    
    try:
        # Pobierz podstawowe statystyki
        strategies = strategy_manager.get_all_strategies()
        channels = channel_manager.get_all_channels()
        configs = config_manager.get_all_configs()
        recent_logs = db_manager.get_admin_logs(limit=10)
        
        stats = {
            'total_strategies': len(strategies),
            'active_strategies': len([s for s in strategies if s.is_active]),
            'total_channels': len(channels),
            'active_channels': len([c for c in channels if c.is_active]),
            'total_configs': len(configs),
            'recent_actions': len(recent_logs)
        }
        
        return render_template('admin_panel.html', stats=stats)
    
    except Exception as e:
        logger.error(f"Error loading admin dashboard: {e}")
        flash(f'Błąd ładowania dashboard: {e}', 'error')
        return render_template('admin_panel.html', stats={})


# ===== STRATEGY MANAGEMENT ROUTES =====

@app.route('/strategies')
def strategies_page():
    """📈 Strona zarządzania strategiami."""
    logger.info("📈 Strategies page requested")
    return render_template('admin_strategies.html')


@app.route('/api/strategies', methods=['GET'])
@handle_api_error
def api_get_strategies():
    """API: Pobierz wszystkie strategie."""
    strategies = strategy_manager.get_all_strategies()
    return jsonify([strategy.to_dict() for strategy in strategies])


@app.route('/api/strategies', methods=['POST'])
@handle_api_error
def api_create_strategy():
    """API: Utwórz nową strategię."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")
    
    strategy = Strategy.from_dict(data)
    strategy_id = strategy_manager.create_strategy(strategy, get_client_ip())
    
    logger.info(f"✅ Created strategy: {strategy.name}")
    return jsonify({'id': strategy_id, 'message': 'Strategia utworzona pomyślnie'}), 201


@app.route('/api/strategies/<int:strategy_id>', methods=['GET'])
@handle_api_error
def api_get_strategy(strategy_id: int):
    """API: Pobierz strategię po ID."""
    strategy = strategy_manager.get_strategy(strategy_id)
    if not strategy:
        return jsonify({'error': 'Strategia nie znaleziona'}), 404
    
    # Dodaj statystyki strategii
    stats = strategy_manager.get_strategy_stats(strategy_id)
    strategy_dict = strategy.to_dict()
    strategy_dict['stats'] = stats
    
    return jsonify(strategy_dict)


@app.route('/api/strategies/<int:strategy_id>', methods=['PUT'])
@handle_api_error
def api_update_strategy(strategy_id: int):
    """API: Aktualizuj strategię."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")
    
    data['id'] = strategy_id
    strategy = Strategy.from_dict(data)
    
    success = strategy_manager.update_strategy(strategy, get_client_ip())
    if success:
        logger.info(f"✅ Updated strategy: {strategy.name}")
        return jsonify({'message': 'Strategia zaktualizowana pomyślnie'})
    else:
        return jsonify({'error': 'Błąd aktualizacji strategii'}), 500


@app.route('/api/strategies/<int:strategy_id>', methods=['DELETE'])
@handle_api_error
def api_delete_strategy(strategy_id: int):
    """API: Usuń strategię."""
    success = strategy_manager.delete_strategy(strategy_id, get_client_ip())
    if success:
        logger.info(f"✅ Deleted strategy ID: {strategy_id}")
        return jsonify({'message': 'Strategia usunięta pomyślnie'})
    else:
        return jsonify({'error': 'Błąd usuwania strategii'}), 500


# ===== CHANNEL MANAGEMENT ROUTES =====

@app.route('/channels')
def channels_page():
    """📺 Strona zarządzania kanałami Discord."""
    logger.info("📺 Channels page requested")
    return render_template('admin_channels.html')


@app.route('/api/channels', methods=['GET'])
@handle_api_error
def api_get_channels():
    """API: Pobierz wszystkie kanały."""
    channels = channel_manager.get_all_channels()
    return jsonify([channel.to_dict() for channel in channels])


@app.route('/api/channels', methods=['POST'])
@handle_api_error
def api_create_channel():
    """API: Utwórz nowy kanał."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")
    
    channel = DiscordChannel.from_dict(data)
    channel_id = channel_manager.create_channel(channel, get_client_ip())
    
    logger.info(f"✅ Created channel: {channel.channel_name}")
    return jsonify({'id': channel_id, 'message': 'Kanał utworzony pomyślnie'}), 201


@app.route('/api/channels/<int:channel_id>', methods=['GET'])
@handle_api_error
def api_get_channel(channel_id: int):
    """API: Pobierz kanał po ID."""
    channel = channel_manager.get_channel(channel_id)
    if not channel:
        return jsonify({'error': 'Kanał nie znaleziony'}), 404
    
    return jsonify(channel.to_dict())


@app.route('/api/channels/<int:channel_id>', methods=['PUT'])
@handle_api_error
def api_update_channel(channel_id: int):
    """API: Aktualizuj kanał."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")
    
    data['id'] = channel_id
    channel = DiscordChannel.from_dict(data)
    
    success = channel_manager.update_channel(channel, get_client_ip())
    if success:
        logger.info(f"✅ Updated channel: {channel.channel_name}")
        return jsonify({'message': 'Kanał zaktualizowany pomyślnie'})
    else:
        return jsonify({'error': 'Błąd aktualizacji kanału'}), 500


@app.route('/api/channels/<int:channel_id>', methods=['DELETE'])
@handle_api_error
def api_delete_channel(channel_id: int):
    """API: Usuń kanał."""
    success = channel_manager.delete_channel(channel_id, get_client_ip())
    if success:
        logger.info(f"✅ Deleted channel ID: {channel_id}")
        return jsonify({'message': 'Kanał usunięty pomyślnie'})
    else:
        return jsonify({'error': 'Błąd usuwania kanału'}), 500


# ===== BOT WHITELIST ROUTES =====

@app.route('/api/channels/<int:channel_id>/bots', methods=['POST'])
@handle_api_error
def api_add_bot_to_whitelist(channel_id: int):
    """API: Dodaj bota do whitelisty kanału."""
    data = request.get_json()
    if not data or 'bot_id' not in data:
        raise ValueError("Bot ID jest wymagane")
    
    bot_id = data['bot_id']
    bot_name = data.get('bot_name')
    
    whitelist_id = channel_manager.add_bot_to_whitelist(
        channel_id, bot_id, bot_name, get_client_ip()
    )
    
    logger.info(f"✅ Added bot {bot_id} to channel {channel_id} whitelist")
    return jsonify({'id': whitelist_id, 'message': 'Bot dodany do whitelisty'}), 201


@app.route('/api/bots/<int:whitelist_id>', methods=['DELETE'])
@handle_api_error
def api_remove_bot_from_whitelist(whitelist_id: int):
    """API: Usuń bota z whitelisty."""
    success = channel_manager.remove_bot_from_whitelist(whitelist_id, get_client_ip())
    if success:
        logger.info(f"✅ Removed bot from whitelist ID: {whitelist_id}")
        return jsonify({'message': 'Bot usunięty z whitelisty'})
    else:
        return jsonify({'error': 'Błąd usuwania bota z whitelisty'}), 500


# ===== CONFIGURATION ROUTES =====

@app.route('/config')
def config_page():
    """⚙️ Strona konfiguracji systemowej."""
    logger.info("⚙️ Config page requested")
    return render_template('admin_config.html')


@app.route('/api/config', methods=['GET'])
@handle_api_error
def api_get_configs():
    """API: Pobierz wszystkie konfiguracje."""
    include_sensitive = request.args.get('include_sensitive', 'false').lower() == 'true'
    configs = config_manager.get_all_configs(include_sensitive)
    
    # Maskuj wrażliwe dane jeśli nie są jawnie żądane
    if not include_sensitive:
        for config in configs:
            if config.is_sensitive:
                config.value = config.get_display_value()
    
    return jsonify([config.to_dict() for config in configs])


@app.route('/api/config', methods=['POST'])
@handle_api_error
def api_create_config():
    """API: Utwórz nową konfigurację."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")
    
    config = SystemConfig.from_dict(data)
    config_id = config_manager.create_config(config, get_client_ip())
    
    logger.info(f"✅ Created config: {config.key}")
    return jsonify({'id': config_id, 'message': 'Konfiguracja utworzona pomyślnie'}), 201


@app.route('/api/config/<int:config_id>', methods=['PUT'])
@handle_api_error
def api_update_config(config_id: int):
    """API: Aktualizuj konfigurację."""
    data = request.get_json()
    if not data:
        raise ValueError("Brak danych JSON")
    
    data['id'] = config_id
    config = SystemConfig.from_dict(data)
    
    success = config_manager.update_config(config, get_client_ip())
    if success:
        logger.info(f"✅ Updated config: {config.key}")
        return jsonify({'message': 'Konfiguracja zaktualizowana pomyślnie'})
    else:
        return jsonify({'error': 'Błąd aktualizacji konfiguracji'}), 500


@app.route('/api/config/<int:config_id>', methods=['DELETE'])
@handle_api_error
def api_delete_config(config_id: int):
    """API: Usuń konfigurację."""
    success = config_manager.delete_config(config_id, get_client_ip())
    if success:
        logger.info(f"✅ Deleted config ID: {config_id}")
        return jsonify({'message': 'Konfiguracja usunięta pomyślnie'})
    else:
        return jsonify({'error': 'Błąd usuwania konfiguracji'}), 500


# ===== LOGS AND MONITORING =====

@app.route('/logs')
def logs_page():
    """📋 Strona logów administratora."""
    logger.info("📋 Logs page requested")
    return render_template('admin_logs.html')


@app.route('/api/logs', methods=['GET'])
@handle_api_error
def api_get_logs():
    """API: Pobierz logi administratora."""
    limit = request.args.get('limit', 100, type=int)
    entity_type = request.args.get('entity_type')
    
    logs = db_manager.get_admin_logs(limit, entity_type)
    return jsonify(logs)


# ===== HEALTH CHECK =====

@app.route('/api/health')
def api_health():
    """API: Health check panelu administratora."""
    try:
        # Sprawdź połączenie z bazą danych
        with db_manager.get_connection() as conn:
            conn.execute("SELECT 1")
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': 'connected',
            'version': '1.0-admin-panel'
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


if __name__ == '__main__':
    print("🔧 Uruchamianie Admin Panel...")
    print("🏠 Panel administratora dostępny pod adresem: http://localhost:5001")
    app.run(debug=True, host='0.0.0.0', port=5001)
