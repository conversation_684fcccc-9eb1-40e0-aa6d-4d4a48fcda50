["tests/unit/test_admin_panel.py::TestBotWhitelist::test_bot_validation", "tests/unit/test_admin_panel.py::TestDatabaseManager::test_admin_logging", "tests/unit/test_admin_panel.py::TestDatabaseManager::test_database_initialization", "tests/unit/test_admin_panel.py::TestDiscordChannel::test_channel_creation", "tests/unit/test_admin_panel.py::TestDiscordChannel::test_channel_validation", "tests/unit/test_admin_panel.py::TestStrategy::test_strategy_creation", "tests/unit/test_admin_panel.py::TestStrategy::test_strategy_from_dict", "tests/unit/test_admin_panel.py::TestStrategy::test_strategy_to_dict", "tests/unit/test_admin_panel.py::TestStrategy::test_strategy_validation", "tests/unit/test_admin_panel.py::TestStrategyManager::test_create_duplicate_strategy", "tests/unit/test_admin_panel.py::TestStrategyManager::test_create_strategy", "tests/unit/test_admin_panel.py::TestStrategyManager::test_delete_strategy", "tests/unit/test_admin_panel.py::TestStrategyManager::test_get_all_strategies", "tests/unit/test_admin_panel.py::TestStrategyManager::test_update_strategy", "tests/unit/test_admin_panel.py::TestSystemConfig::test_config_creation", "tests/unit/test_admin_panel.py::TestSystemConfig::test_config_validation", "tests/unit/test_admin_panel.py::TestSystemConfig::test_display_value", "tests/unit/test_admin_panel.py::TestSystemConfig::test_set_typed_value", "tests/unit/test_admin_panel.py::TestSystemConfig::test_typed_values"]