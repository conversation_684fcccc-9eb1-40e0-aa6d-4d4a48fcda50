#!/usr/bin/env python3
"""
Discord Channel Model for Admin Panel
=====================================

Model kanału Discord z pełną funkcjonalnością CRUD,
zarządzaniem whitelistą botów i integracją ze strategiami.

Autor: AI Assistant & Full Stack Developer
Data: 2025-06-16
"""

import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from .database import DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class DiscordChannel:
    """
    📺 Model kanału Discord

    Reprezentuje kanał Discord z przypisaną strategią
    i konfiguracją whitelisty botów.
    """
    id: Optional[int] = None
    channel_id: str = ""
    channel_name: str = ""
    guild_id: Optional[str] = None
    guild_name: Optional[str] = None
    strategy_id: Optional[int] = None
    is_active: bool = True
    allow_bot_messages: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # Dodatkowe pola (nie w bazie danych)
    strategy_name: Optional[str] = None
    bot_whitelist: Optional[List[Dict]] = None

    def __post_init__(self):
        """Walidacja po inicjalizacji."""
        self.validate()

    def validate(self) -> bool:
        """
        Waliduje parametry kanału.

        Returns:
            True jeśli walidacja przeszła pomyślnie

        Raises:
            ValueError: Jeśli parametry są nieprawidłowe
        """
        if not self.channel_id or not self.channel_id.strip():
            raise ValueError("ID kanału Discord jest wymagane")

        if not self.channel_name or len(self.channel_name.strip()) < 2:
            raise ValueError("Nazwa kanału musi mieć co najmniej 2 znaki")

        # Sprawdź czy channel_id to liczba (Discord ID)
        try:
            int(self.channel_id)
        except ValueError:
            raise ValueError("ID kanału Discord musi być liczbą")

        return True

    def to_dict(self) -> Dict[str, Any]:
        """Konwertuje kanał do słownika."""
        data = asdict(self)
        # Konwertuj datetime do string dla JSON
        if data['created_at']:
            data['created_at'] = data['created_at'].isoformat()
        if data['updated_at']:
            data['updated_at'] = data['updated_at'].isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DiscordChannel':
        """Tworzy kanał ze słownika."""
        # Konwertuj string datetime z powrotem do datetime
        if data.get('created_at') and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('updated_at') and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])

        return cls(**data)


@dataclass
class BotWhitelist:
    """
    🤖 Model bota na whiteliście
    """
    id: Optional[int] = None
    channel_id: int = 0
    bot_id: str = ""
    bot_name: Optional[str] = None
    is_active: bool = True
    created_at: Optional[datetime] = None

    def validate(self) -> bool:
        """Waliduje parametry bota."""
        if not self.bot_id or not self.bot_id.strip():
            raise ValueError("ID bota Discord jest wymagane")

        try:
            int(self.bot_id)
        except ValueError:
            raise ValueError("ID bota Discord musi być liczbą")

        return True

    def __post_init__(self):
        """Walidacja po inicjalizacji."""
        self.validate()


class ChannelManager:
    """
    🔧 Manager kanałów Discord

    Zarządza operacjami CRUD dla kanałów Discord i whitelisty botów.
    """

    def __init__(self, db_manager: DatabaseManager):
        """
        Inicjalizacja ChannelManager.

        Args:
            db_manager: Instance DatabaseManager
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    def create_channel(self, channel: DiscordChannel, user_ip: Optional[str] = None) -> int:
        """
        Tworzy nowy kanał Discord.

        Args:
            channel: Obiekt kanału do utworzenia
            user_ip: IP użytkownika dla logów

        Returns:
            ID utworzonego kanału

        Raises:
            ValueError: Jeśli kanał o tym ID już istnieje
        """
        channel.validate()

        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Sprawdź czy kanał o tym ID już istnieje
            cursor.execute("SELECT id FROM discord_channels WHERE channel_id = ?", (channel.channel_id,))
            if cursor.fetchone():
                raise ValueError(f"Kanał o ID '{channel.channel_id}' już istnieje")

            # Wstaw nowy kanał
            cursor.execute('''
                INSERT INTO discord_channels
                (channel_id, channel_name, guild_id, guild_name, strategy_id, is_active, allow_bot_messages)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                channel.channel_id, channel.channel_name, channel.guild_id,
                channel.guild_name, channel.strategy_id, channel.is_active,
                channel.allow_bot_messages
            ))

            channel_db_id = cursor.lastrowid
            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'CREATE', 'channel', channel_db_id,
                new_values=channel.to_dict(), user_ip=user_ip
            )

            self.logger.info(f"✅ Created channel: {channel.channel_name} (ID: {channel_db_id})")
            return channel_db_id

    def get_channel(self, channel_db_id: int) -> Optional[DiscordChannel]:
        """
        Pobiera kanał po ID bazy danych.

        Args:
            channel_db_id: ID kanału w bazie danych

        Returns:
            Obiekt DiscordChannel lub None jeśli nie znaleziono
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT dc.*, s.name as strategy_name
                FROM discord_channels dc
                LEFT JOIN strategies s ON dc.strategy_id = s.id
                WHERE dc.id = ?
            ''', (channel_db_id,))
            row = cursor.fetchone()

            if row:
                data = dict(row)
                strategy_name = data.pop('strategy_name', None)
                channel = DiscordChannel(**data)
                channel.strategy_name = strategy_name
                channel.bot_whitelist = self.get_channel_bots(channel_db_id)
                return channel
            return None

    def get_channel_by_discord_id(self, discord_channel_id: str) -> Optional[DiscordChannel]:
        """
        Pobiera kanał po Discord ID.

        Args:
            discord_channel_id: ID kanału Discord

        Returns:
            Obiekt DiscordChannel lub None jeśli nie znaleziono
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT dc.*, s.name as strategy_name
                FROM discord_channels dc
                LEFT JOIN strategies s ON dc.strategy_id = s.id
                WHERE dc.channel_id = ?
            ''', (discord_channel_id,))
            row = cursor.fetchone()

            if row:
                data = dict(row)
                strategy_name = data.pop('strategy_name', None)
                channel = DiscordChannel(**data)
                channel.strategy_name = strategy_name
                channel.bot_whitelist = self.get_channel_bots(channel.id)
                return channel
            return None

    def get_all_channels(self, active_only: bool = False) -> List[DiscordChannel]:
        """
        Pobiera wszystkie kanały.

        Args:
            active_only: Czy pobrać tylko aktywne kanały

        Returns:
            Lista kanałów
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            query = '''
                SELECT dc.*, s.name as strategy_name
                FROM discord_channels dc
                LEFT JOIN strategies s ON dc.strategy_id = s.id
            '''
            if active_only:
                query += " WHERE dc.is_active = 1"
            query += " ORDER BY dc.channel_name"

            cursor.execute(query)
            channels = []
            for row in cursor.fetchall():
                data = dict(row)
                strategy_name = data.pop('strategy_name', None)
                channel = DiscordChannel(**data)
                channel.strategy_name = strategy_name
                channel.bot_whitelist = self.get_channel_bots(channel.id)
                channels.append(channel)

            return channels

    def update_channel(self, channel: DiscordChannel, user_ip: Optional[str] = None) -> bool:
        """
        Aktualizuje kanał.

        Args:
            channel: Obiekt kanału z zaktualizowanymi danymi
            user_ip: IP użytkownika dla logów

        Returns:
            True jeśli aktualizacja się powiodła

        Raises:
            ValueError: Jeśli kanał nie istnieje lub Discord ID jest zajęte
        """
        if not channel.id:
            raise ValueError("ID kanału jest wymagane do aktualizacji")

        channel.validate()

        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Pobierz stare wartości dla logów
            old_channel = self.get_channel(channel.id)
            if not old_channel:
                raise ValueError(f"Kanał o ID {channel.id} nie istnieje")

            # Sprawdź czy Discord ID nie jest zajęte przez inny kanał
            cursor.execute(
                "SELECT id FROM discord_channels WHERE channel_id = ? AND id != ?",
                (channel.channel_id, channel.id)
            )
            if cursor.fetchone():
                raise ValueError(f"Kanał o Discord ID '{channel.channel_id}' już istnieje")

            # Aktualizuj kanał
            cursor.execute('''
                UPDATE discord_channels SET
                    channel_id = ?, channel_name = ?, guild_id = ?, guild_name = ?,
                    strategy_id = ?, is_active = ?, allow_bot_messages = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                channel.channel_id, channel.channel_name, channel.guild_id,
                channel.guild_name, channel.strategy_id, channel.is_active,
                channel.allow_bot_messages, channel.id
            ))

            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'UPDATE', 'channel', channel.id,
                old_values=old_channel.to_dict(),
                new_values=channel.to_dict(),
                user_ip=user_ip
            )

            self.logger.info(f"✅ Updated channel: {channel.channel_name} (ID: {channel.id})")
            return True

    def delete_channel(self, channel_id: int, user_ip: Optional[str] = None) -> bool:
        """
        Usuwa kanał.

        Args:
            channel_id: ID kanału do usunięcia
            user_ip: IP użytkownika dla logów

        Returns:
            True jeśli usunięcie się powiodło
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Pobierz kanał dla logów
            old_channel = self.get_channel(channel_id)
            if not old_channel:
                raise ValueError(f"Kanał o ID {channel_id} nie istnieje")

            # Usuń najpierw boty z whitelisty
            cursor.execute("DELETE FROM bot_whitelist WHERE channel_id = ?", (channel_id,))

            # Usuń kanał
            cursor.execute("DELETE FROM discord_channels WHERE id = ?", (channel_id,))
            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'DELETE', 'channel', channel_id,
                old_values=old_channel.to_dict(),
                user_ip=user_ip
            )

            self.logger.info(f"✅ Deleted channel: {old_channel.channel_name} (ID: {channel_id})")
            return True

    def add_bot_to_whitelist(self, channel_id: int, bot_id: str, bot_name: Optional[str] = None,
                           user_ip: Optional[str] = None) -> int:
        """
        Dodaje bota do whitelisty kanału.

        Args:
            channel_id: ID kanału w bazie danych
            bot_id: Discord ID bota
            bot_name: Nazwa bota (opcjonalna)
            user_ip: IP użytkownika dla logów

        Returns:
            ID wpisu w whiteliście
        """
        bot = BotWhitelist(channel_id=channel_id, bot_id=bot_id, bot_name=bot_name)
        bot.validate()

        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Sprawdź czy bot już jest na whiteliście
            cursor.execute(
                "SELECT id FROM bot_whitelist WHERE channel_id = ? AND bot_id = ?",
                (channel_id, bot_id)
            )
            if cursor.fetchone():
                raise ValueError(f"Bot {bot_id} już jest na whiteliście tego kanału")

            # Dodaj bota
            cursor.execute('''
                INSERT INTO bot_whitelist (channel_id, bot_id, bot_name, is_active)
                VALUES (?, ?, ?, ?)
            ''', (channel_id, bot_id, bot_name, True))

            bot_whitelist_id = cursor.lastrowid
            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'CREATE', 'bot_whitelist', bot_whitelist_id,
                new_values={'channel_id': channel_id, 'bot_id': bot_id, 'bot_name': bot_name},
                user_ip=user_ip
            )

            self.logger.info(f"✅ Added bot {bot_id} to whitelist for channel {channel_id}")
            return bot_whitelist_id

    def remove_bot_from_whitelist(self, whitelist_id: int, user_ip: Optional[str] = None) -> bool:
        """
        Usuwa bota z whitelisty.

        Args:
            whitelist_id: ID wpisu w whiteliście
            user_ip: IP użytkownika dla logów

        Returns:
            True jeśli usunięcie się powiodło
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # Pobierz dane bota dla logów
            cursor.execute("SELECT * FROM bot_whitelist WHERE id = ?", (whitelist_id,))
            row = cursor.fetchone()
            if not row:
                raise ValueError(f"Wpis whitelisty o ID {whitelist_id} nie istnieje")

            old_data = dict(row)

            # Usuń bota
            cursor.execute("DELETE FROM bot_whitelist WHERE id = ?", (whitelist_id,))
            conn.commit()

            # Loguj akcję
            self.db_manager.log_admin_action(
                'DELETE', 'bot_whitelist', whitelist_id,
                old_values=old_data,
                user_ip=user_ip
            )

            self.logger.info(f"✅ Removed bot from whitelist (ID: {whitelist_id})")
            return True

    def get_channel_bots(self, channel_id: int) -> List[Dict[str, Any]]:
        """
        Pobiera listę botów na whiteliście kanału.

        Args:
            channel_id: ID kanału w bazie danych

        Returns:
            Lista botów na whiteliście
        """
        with self.db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM bot_whitelist WHERE channel_id = ? ORDER BY bot_name, bot_id",
                (channel_id,)
            )
            return [dict(row) for row in cursor.fetchall()]
