2025-06-16 03:23:19,564 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-16 03:23:19,564 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-16 03:23:19,792 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:23:20,315 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:23:20,333 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:23:40,712 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\werkzeug\\debug\\__init__.py', reloading
2025-06-16 03:23:40,747 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\werkzeug\\serving.py', reloading
2025-06-16 03:23:40,770 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:40] "GET / HTTP/1.1" 200 -
2025-06-16 03:23:41,144 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:23:41,659 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:23:42,227 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:23:42,244 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:23:42,445 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:42] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-16 03:23:48,619 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:48] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:23:58,123 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:58] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:24:11,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:24:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:24:32,562 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\start_servers.py', reloading
2025-06-16 03:24:32,568 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\start_servers.py', reloading
2025-06-16 03:24:32,743 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:24:33,061 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:24:33,068 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:24:41,652 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:24:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:25:11,339 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:25:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:25:41,644 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:25:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:11,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:23,980 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:23] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:26:24,038 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:24] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:26:25,984 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:25] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:26,297 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:26] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:26,493 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:26] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:27,368 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:26:27,566 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:26:27,729 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:26:30,082 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:30] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:30,127 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:27:00,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:27:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:27:30,346 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:27:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:28:00,651 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:28:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:28:30,350 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:28:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:00,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:29:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:21,046 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:21,047 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:21,774 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:22,083 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:22,093 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:29:30,646 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:29:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:34,580 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:34,581 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:35,184 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:35,446 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:35,456 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:29:54,519 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:54,519 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:54,662 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:55,022 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:55,035 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:30:13,426 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:13,427 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:13,428 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:14,205 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:30:14,518 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:30:14,531 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:30:43,646 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:30:43] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:30:44,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:44,361 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:44,813 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:30:45,110 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:30:45,123 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:31:05,781 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:05,782 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:05,784 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:06,375 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:31:06,838 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:31:06,852 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:31:44,656 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:31:44] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:32:45,652 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:32:45] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:33:46,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:33:46] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:34:47,647 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:34:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:35:47,644 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:35:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:35:59,179 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:35:59] "GET / HTTP/1.1" 200 -
2025-06-16 03:35:59,467 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:35:59] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:02,404 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:02] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:36:02,486 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:02] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:19,711 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:19] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:36:19,788 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:19] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:36:19,789 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:19] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:29,667 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:29] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:30,035 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:30] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:36:30,333 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:30] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:47,669 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:48,301 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:48] "[35m[1mPOST /api/strategies HTTP/1.1[0m" 201 -
2025-06-16 03:36:48,624 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:48] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:36:51,282 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:51] "GET / HTTP/1.1" 200 -
2025-06-16 03:36:51,570 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:51] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:36:52,533 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:52] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:36:52,601 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:52] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:36:52,789 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:36:52] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:37:26,949 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:26] "[35m[1mPOST /api/channels HTTP/1.1[0m" 201 -
2025-06-16 03:37:27,277 - __main__ - ERROR - API error in api_get_channels: 'str' object has no attribute 'isoformat'
2025-06-16 03:37:27,279 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:27] "[35m[1mGET /api/channels HTTP/1.1[0m" 500 -
2025-06-16 03:37:30,125 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:30] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-16 03:37:47,820 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:37:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:38:47,342 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:38:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:39:47,657 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:39:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:40:47,343 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:40:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:41:47,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:41:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:42:47,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:42:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:43:47,667 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:43:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:44:47,349 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:44:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:45:41,062 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:41,064 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:41,953 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:45:44,381 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:45:44,392 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:45:47,645 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:45:47] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:45:55,808 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:55,811 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:45:56,678 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:45:57,079 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:45:57,092 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:46:09,371 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:09,373 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:10,255 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:46:10,606 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:46:10,617 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:46:23,001 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:23,002 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\models\\channel.py', reloading
2025-06-16 03:46:23,797 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:46:24,253 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:46:24,270 - werkzeug - INFO -  * Debugger PIN: 751-346-757
