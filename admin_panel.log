2025-06-16 03:23:19,564 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
2025-06-16 03:23:19,564 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-16 03:23:19,792 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:23:20,315 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:23:20,333 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:23:40,712 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\werkzeug\\debug\\__init__.py', reloading
2025-06-16 03:23:40,747 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python312\\site-packages\\werkzeug\\serving.py', reloading
2025-06-16 03:23:40,770 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:40] "GET / HTTP/1.1" 200 -
2025-06-16 03:23:41,144 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:23:41,659 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:23:42,227 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:23:42,244 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:23:42,445 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:42] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-16 03:23:48,619 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:48] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:23:58,123 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:23:58] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:24:11,345 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:24:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:24:32,562 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\start_servers.py', reloading
2025-06-16 03:24:32,568 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\start_servers.py', reloading
2025-06-16 03:24:32,743 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:24:33,061 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:24:33,068 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:24:41,652 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:24:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:25:11,339 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:25:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:25:41,644 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:25:41] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:11,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:11] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:23,980 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:23] "GET /strategies HTTP/1.1" 200 -
2025-06-16 03:26:24,038 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:24] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:26:25,984 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:25] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:26,297 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:26] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:26,493 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:26] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:26:27,368 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /channels HTTP/1.1" 200 -
2025-06-16 03:26:27,566 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /api/channels HTTP/1.1" 200 -
2025-06-16 03:26:27,729 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:27] "GET /api/strategies HTTP/1.1" 200 -
2025-06-16 03:26:30,082 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:30] "GET / HTTP/1.1" 200 -
2025-06-16 03:26:30,127 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:26:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:27:00,653 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:27:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:27:30,346 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:27:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:28:00,651 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:28:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:28:30,350 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:28:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:00,650 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:29:00] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:21,046 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:21,047 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:21,774 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:22,083 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:22,093 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:29:30,646 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:29:30] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:29:34,580 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:34,581 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:35,184 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:35,446 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:35,456 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:29:54,519 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:54,519 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:29:54,662 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:29:55,022 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:29:55,035 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:30:13,426 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:13,427 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:13,428 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:14,205 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:30:14,518 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:30:14,531 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:30:43,646 - werkzeug - INFO - 127.0.0.1 - - [16/Jun/2025 03:30:43] "GET /api/health HTTP/1.1" 200 -
2025-06-16 03:30:44,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:44,361 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:30:44,813 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:30:45,110 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:30:45,123 - werkzeug - INFO -  * Debugger PIN: 751-************-06-16 03:31:05,781 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:05,782 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:05,784 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\98989\\discord_bybit_signal_monitor.py', reloading
2025-06-16 03:31:06,375 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-16 03:31:06,838 - werkzeug - WARNING -  * Debugger is active!
2025-06-16 03:31:06,852 - werkzeug - INFO -  * Debugger PIN: 751-346-757
